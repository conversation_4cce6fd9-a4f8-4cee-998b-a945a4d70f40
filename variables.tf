variable "aws_region" {
  description = "AWS region to deploy the EKS cluster"
  type        = string
  default     = "us-east-1"
}

variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "cluster_version" {
  description = "Kubernetes version to use for the EKS cluster"
  type        = string
  default     = "1.28"
}

variable "app_environment" {
  description = "Environment name (e.g., production, staging, development)"
  type        = string
}

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "owner" {
  description = "Owner of the resources"
  type        = string
}

variable "vpc_cidr" {
  description = "CIDR block for the VPC"
  type        = string
}

variable "availability_zones" {
  description = "List of availability zones to use"
  type        = list(string)
}

variable "private_subnet_cidrs" {
  description = "List of CIDR blocks for private subnets"
  type        = list(string)
}

variable "public_subnet_cidrs" {
  description = "List of CIDR blocks for public subnets"
  type        = list(string)
}

variable "node_groups" {
  description = "Map of node group configurations"
  type = map(object({
    desired_size    = number
    min_size        = number
    max_size        = number
    instance_types  = list(string)
    disk_size       = number
    disk_type       = string
    ami_type        = string
    capacity_type   = string
  }))
}

variable "enable_secrets_encryption" {
  description = "Enable KMS encryption for EKS secrets"
  type        = bool
  default     = true
}

variable "enable_cloudwatch_logging" {
  description = "Enable CloudWatch logging for the EKS cluster"
  type        = bool
  default     = true
}

variable "enable_guardduty" {
  description = "Enable AWS GuardDuty for the EKS cluster"
  type        = bool
  default     = true
}

variable "enable_ebs_csi_driver" {
  description = "Enable EBS CSI driver"
  type        = bool
  default     = true
}

variable "enable_aws_load_balancer_controller" {
  description = "Enable AWS Load Balancer Controller"
  type        = bool
  default     = true
}

variable "cluster_endpoint_private_access" {
  description = "Enable private access to the EKS cluster endpoint"
  type        = bool
  default     = true
}

variable "cluster_endpoint_public_access" {
  description = "Enable public access to the EKS cluster endpoint"
  type        = bool
  default     = true
}

variable "cluster_endpoint_public_access_cidrs" {
  description = "List of CIDR blocks allowed to access the EKS cluster endpoint"
  type        = list(string)
  default     = []
}

variable "additional_node_security_group_rules" {
  description = "Additional security group rules for node groups"
  type = list(object({
    type        = string
    description = string
    protocol    = string
    from_port   = number
    to_port     = number
    cidr_blocks = list(string)
  }))
  default = []
}

variable "vpc_cni_config" {
  description = "Configuration for the VPC CNI plugin"
  type = object({
    warm_ip_target      = number
    minimum_ip_target   = number
    max_pods_per_node   = number
    prefix_delegation   = bool
    enable_prefix_delegation = bool
  })
  default = {
    warm_ip_target      = 5
    minimum_ip_target   = 10
    max_pods_per_node   = 110
    prefix_delegation   = true
    enable_prefix_delegation = true
  }
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}

variable "cloudwatch_log_retention_days" {
  description = "Number of days to retain CloudWatch logs"
  type        = number
  default     = 30
}

variable "kms_key_deletion_window_in_days" {
  description = "Number of days to wait before deleting KMS key"
  type        = number
  default     = 7
}

variable "kms_key_enable_key_rotation" {
  description = "Enable automatic key rotation for KMS key"
  type        = bool
  default     = true
}

variable "ebs_csi_driver_config" {
  description = "Configuration for the EBS CSI driver"
  type = object({
    enable_encryption = bool
    volume_type      = string
    iops_per_gb      = number
    throughput       = number
  })
  default = {
    enable_encryption = true
    volume_type      = "gp3"
    iops_per_gb      = 3000
    throughput       = 125
  }
}

variable "aws_load_balancer_controller_config" {
  description = "Configuration for the AWS Load Balancer Controller"
  type = object({
    enable_ssl_termination = bool
    enable_waf            = bool
    enable_shield         = bool
    enable_access_logs    = bool
  })
  default = {
    enable_ssl_termination = true
    enable_waf            = true
    enable_shield         = true
    enable_access_logs    = true
  }
}

variable "guardduty_config" {
  description = "Configuration for AWS GuardDuty"
  type = object({
    enable_s3_protection = bool
    enable_kubernetes_audit_logs = bool
    enable_malware_protection    = bool
    enable_eks_runtime_monitoring = bool
  })
  default = {
    enable_s3_protection = true
    enable_kubernetes_audit_logs = true
    enable_malware_protection    = true
    enable_eks_runtime_monitoring = true
  }
}

variable "monitoring_config" {
  description = "Configuration for monitoring tools"
  type = object({
    enable_prometheus = bool
    enable_grafana    = bool
    enable_alertmanager = bool
    retention_days    = number
  })
  default = {
    enable_prometheus = true
    enable_grafana    = true
    enable_alertmanager = true
    retention_days    = 15
  }
}

variable "backup_config" {
  description = "Configuration for backup settings"
  type = object({
    enable_etcd_backup = bool
    enable_volume_backup = bool
    backup_retention_days = number
    backup_schedule      = string
  })
  default = {
    enable_etcd_backup = true
    enable_volume_backup = true
    backup_retention_days = 30
    backup_schedule      = "0 0 * * *"
  }
}

# Missing variables referenced in main.tf
variable "enable_irsa" {
  description = "Enable IAM Roles for Service Accounts (IRSA)"
  type        = bool
  default     = true
}

variable "trusted_ips_for_api" {
  description = "List of trusted IP addresses/CIDR blocks for API server access"
  type        = list(string)
  default     = []
}

variable "mng_instance_types" {
  description = "Instance types for managed node groups"
  type        = list(string)
  default     = ["t3.medium"]
}

variable "mng_disk_size_gb" {
  description = "Disk size in GB for managed node groups"
  type        = number
  default     = 20
}

variable "mng_min_nodes" {
  description = "Minimum number of nodes in managed node groups"
  type        = number
  default     = 1
}

variable "mng_max_nodes" {
  description = "Maximum number of nodes in managed node groups"
  type        = number
  default     = 3
}

variable "mng_desired_nodes" {
  description = "Desired number of nodes in managed node groups"
  type        = number
  default     = 2
}

variable "mng_labels" {
  description = "Labels to apply to managed node groups"
  type        = map(string)
  default     = {}
}

variable "mng_taints" {
  description = "Taints to apply to managed node groups"
  type = list(object({
    key    = string
    value  = string
    effect = string
  }))
  default = []
}
