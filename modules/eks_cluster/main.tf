locals {
  common_tags = {
    Environment  = var.app_environment
    ProjectName = var.project_name
    Owner       = var.owner
    ManagedBy   = "terraform"
  }
}

# KMS Key for EKS Secrets Encryption
resource "aws_kms_key" "eks" {
  count                   = var.enable_secrets_encryption ? 1 : 0
  description             = "KMS key for EKS secrets encryption"
  deletion_window_in_days = 7
  enable_key_rotation     = true

  tags = merge(
    local.common_tags,
    {
      Name = "${var.cluster_name}-kms-key"
    }
  )
}

resource "aws_kms_alias" "eks" {
  count         = var.enable_secrets_encryption ? 1 : 0
  name          = "alias/${var.cluster_name}-kms-key"
  target_key_id = aws_kms_key.eks[0].key_id
}

# EKS Cluster
resource "aws_eks_cluster" "main" {
  name     = var.cluster_name
  role_arn = var.cluster_role_arn
  version  = var.cluster_version

  vpc_config {
    subnet_ids              = var.subnet_ids
    security_group_ids      = var.security_group_ids
    endpoint_private_access = true
    endpoint_public_access  = length(var.trusted_ips_for_api) > 0
    public_access_cidrs     = var.trusted_ips_for_api
  }

  encryption_config {
    resources = ["secrets"]
    provider {
      key_arn = var.enable_secrets_encryption ? aws_kms_key.eks[0].arn : null
    }
  }

  enabled_cluster_log_types = var.enable_cloudwatch_logging ? [
    "api",
    "audit",
    "authenticator",
    "controllerManager",
    "scheduler"
  ] : []

  depends_on = [
    aws_iam_role_policy_attachment.cluster_policy
  ]

  tags = local.common_tags
}

# IAM OIDC Provider
resource "aws_iam_openid_connect_provider" "main" {
  count           = var.enable_irsa ? 1 : 0
  client_id_list  = ["sts.amazonaws.com"]
  thumbprint_list = ["9e99a48a9960b14926bb7f3b02e22da2b0ab7280"]
  url             = aws_eks_cluster.main.identity[0].oidc[0].issuer

  tags = merge(
    local.common_tags,
    {
      Name = "${var.cluster_name}-oidc-provider"
    }
  )
}

# CloudWatch Log Group
resource "aws_cloudwatch_log_group" "main" {
  count             = var.enable_cloudwatch_logging ? 1 : 0
  name              = "/aws/eks/${var.cluster_name}/cluster"
  retention_in_days = 30

  tags = local.common_tags
}
