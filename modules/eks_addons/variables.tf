variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "app_environment" {
  description = "Environment name"
  type        = string
}

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "owner" {
  description = "Owner of the resources"
  type        = string
}

variable "enable_vpc_cni" {
  description = "Enable VPC CNI add-on"
  type        = bool
  default     = true
}

variable "enable_coredns" {
  description = "Enable CoreDNS add-on"
  type        = bool
  default     = true
}

variable "enable_kube_proxy" {
  description = "Enable kube-proxy add-on"
  type        = bool
  default     = true
}

variable "enable_ebs_csi_driver" {
  description = "Enable EBS CSI driver add-on"
  type        = bool
  default     = true
}

variable "enable_cloudwatch_observability" {
  description = "Enable CloudWatch observability add-on"
  type        = bool
  default     = true
}

variable "enable_guardduty" {
  description = "Enable GuardDuty agent add-on"
  type        = bool
  default     = true
}

variable "ebs_csi_driver_role_arn" {
  description = "ARN of the EBS CSI driver IAM role"
  type        = string
  default     = ""
}

variable "vpc_cni_config" {
  description = "Configuration for VPC CNI add-on"
  type = object({
    enable_prefix_delegation = bool
    warm_eni_target         = number
  })
  default = {
    enable_prefix_delegation = true
    warm_eni_target         = 1
  }
}
