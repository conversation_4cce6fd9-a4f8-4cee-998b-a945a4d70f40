output "vpc_cni_addon" {
  description = "VPC CNI add-on details"
  value = var.enable_vpc_cni ? {
    name    = aws_eks_addon.vpc_cni[0].addon_name
    version = aws_eks_addon.vpc_cni[0].addon_version
    arn     = aws_eks_addon.vpc_cni[0].arn
  } : null
}

output "coredns_addon" {
  description = "CoreDNS add-on details"
  value = var.enable_coredns ? {
    name    = aws_eks_addon.coredns[0].addon_name
    version = aws_eks_addon.coredns[0].addon_version
    arn     = aws_eks_addon.coredns[0].arn
  } : null
}

output "kube_proxy_addon" {
  description = "kube-proxy add-on details"
  value = var.enable_kube_proxy ? {
    name    = aws_eks_addon.kube_proxy[0].addon_name
    version = aws_eks_addon.kube_proxy[0].addon_version
    arn     = aws_eks_addon.kube_proxy[0].arn
  } : null
}

output "ebs_csi_driver_addon" {
  description = "EBS CSI Driver add-on details"
  value = var.enable_ebs_csi_driver ? {
    name    = aws_eks_addon.ebs_csi_driver[0].addon_name
    version = aws_eks_addon.ebs_csi_driver[0].addon_version
    arn     = aws_eks_addon.ebs_csi_driver[0].arn
  } : null
}

output "cloudwatch_observability_addon" {
  description = "CloudWatch Observability add-on details"
  value = var.enable_cloudwatch_observability ? {
    name    = aws_eks_addon.cloudwatch_observability[0].addon_name
    version = aws_eks_addon.cloudwatch_observability[0].addon_version
    arn     = aws_eks_addon.cloudwatch_observability[0].arn
  } : null
}

output "guardduty_addon" {
  description = "GuardDuty Agent add-on details"
  value = var.enable_guardduty ? {
    name    = aws_eks_addon.guardduty[0].addon_name
    version = aws_eks_addon.guardduty[0].addon_version
    arn     = aws_eks_addon.guardduty[0].arn
  } : null
}
