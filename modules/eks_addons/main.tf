locals {
  common_tags = {
    Environment  = var.app_environment
    ProjectName = var.project_name
    Owner       = var.owner
    ManagedBy   = "terraform"
  }
}

# VPC CNI Add-on
resource "aws_eks_addon" "vpc_cni" {
  count                    = var.enable_vpc_cni ? 1 : 0
  cluster_name             = var.cluster_name
  addon_name               = "vpc-cni"
  addon_version            = "v1.15.4-eksbuild.1"
  resolve_conflicts_on_create = "OVERWRITE"
  resolve_conflicts_on_update = "OVERWRITE"

  configuration_values = jsonencode({
    enablePrefixDelegation = var.vpc_cni_config.enable_prefix_delegation
    warmENITarget         = var.vpc_cni_config.warm_eni_target
  })

  tags = local.common_tags
}

# CoreDNS Add-on
resource "aws_eks_addon" "coredns" {
  count                    = var.enable_coredns ? 1 : 0
  cluster_name             = var.cluster_name
  addon_name               = "coredns"
  addon_version            = "v1.10.1-eksbuild.5"
  resolve_conflicts_on_create = "OVERWRITE"
  resolve_conflicts_on_update = "OVERWRITE"

  tags = local.common_tags
}

# kube-proxy Add-on
resource "aws_eks_addon" "kube_proxy" {
  count                    = var.enable_kube_proxy ? 1 : 0
  cluster_name             = var.cluster_name
  addon_name               = "kube-proxy"
  addon_version            = "v1.28.2-eksbuild.2"
  resolve_conflicts_on_create = "OVERWRITE"
  resolve_conflicts_on_update = "OVERWRITE"

  tags = local.common_tags
}

# EBS CSI Driver Add-on
resource "aws_eks_addon" "ebs_csi_driver" {
  count                    = var.enable_ebs_csi_driver ? 1 : 0
  cluster_name             = var.cluster_name
  addon_name               = "aws-ebs-csi-driver"
  addon_version            = "v1.24.1-eksbuild.1"
  resolve_conflicts_on_create = "OVERWRITE"
  resolve_conflicts_on_update = "OVERWRITE"
  service_account_role_arn = var.ebs_csi_driver_role_arn

  tags = local.common_tags
}

# CloudWatch Observability Add-on
resource "aws_eks_addon" "cloudwatch_observability" {
  count                    = var.enable_cloudwatch_observability ? 1 : 0
  cluster_name             = var.cluster_name
  addon_name               = "amazon-cloudwatch-observability"
  addon_version            = "v1.2.1-eksbuild.1"
  resolve_conflicts_on_create = "OVERWRITE"
  resolve_conflicts_on_update = "OVERWRITE"

  tags = local.common_tags
}

# GuardDuty Agent Add-on
resource "aws_eks_addon" "guardduty" {
  count                    = var.enable_guardduty ? 1 : 0
  cluster_name             = var.cluster_name
  addon_name               = "aws-guardduty-agent"
  addon_version            = "v1.3.1-eksbuild.1"
  resolve_conflicts_on_create = "OVERWRITE"
  resolve_conflicts_on_update = "OVERWRITE"

  tags = local.common_tags
}
