variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "app_environment" {
  description = "Environment name"
  type        = string
}

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "owner" {
  description = "Owner of the resources"
  type        = string
}

variable "node_role_arn" {
  description = "ARN of the EKS node group IAM role"
  type        = string
}

variable "subnet_ids" {
  description = "List of subnet IDs for the node groups"
  type        = list(string)
}

variable "instance_types" {
  description = "List of instance types for the node groups"
  type        = list(string)
  default     = ["m5.large", "m5.xlarge"]
}

variable "disk_size" {
  description = "Disk size in GB for the node groups"
  type        = number
  default     = 50
}

variable "min_size" {
  description = "Minimum number of nodes in the node groups"
  type        = number
  default     = 2
}

variable "max_size" {
  description = "Maximum number of nodes in the node groups"
  type        = number
  default     = 5
}

variable "desired_size" {
  description = "Desired number of nodes in the node groups"
  type        = number
  default     = 3
}

variable "labels" {
  description = "Labels for the node groups"
  type        = map(string)
  default     = {
    "workload-type" = "general-purpose"
  }
}

variable "taints" {
  description = "Taints for the node groups"
  type = list(object({
    key    = string
    value  = string
    effect = string
  }))
  default = []
}

variable "ami_type" {
  description = "AMI type for the node groups"
  type        = string
  default     = "AL2_x86_64"
}

variable "capacity_type" {
  description = "Capacity type for the node groups"
  type        = string
  default     = "ON_DEMAND"
}

variable "update_config" {
  description = "Update configuration for the node groups"
  type = object({
    max_unavailable_percentage = number
  })
  default = {
    max_unavailable_percentage = 33
  }
}
