locals {
  common_tags = {
    Environment  = var.app_environment
    ProjectName = var.project_name
    Owner       = var.owner
    ManagedBy   = "terraform"
  }
}

# Managed Node Group
resource "aws_eks_node_group" "main" {
  cluster_name    = var.cluster_name
  node_group_name = "mng-general-purpose-${var.cluster_name}"
  node_role_arn   = var.node_role_arn
  subnet_ids      = var.subnet_ids

  instance_types = var.instance_types
  ami_type       = var.ami_type
  disk_size      = var.disk_size
  capacity_type  = var.capacity_type

  scaling_config {
    min_size     = var.min_size
    max_size     = var.max_size
    desired_size = var.desired_size
  }

  update_config {
    max_unavailable_percentage = var.update_config.max_unavailable_percentage
  }

  labels = var.labels

  dynamic "taint" {
    for_each = var.taints
    content {
      key    = taint.value.key
      value  = taint.value.value
      effect = taint.value.effect
    }
  }

  tags = merge(
    local.common_tags,
    {
      Name = "mng-general-purpose-${var.cluster_name}"
    }
  )

  lifecycle {
    create_before_destroy = true
  }
}
