output "cluster_id" {
  description = "The name/id of the EKS cluster"
  value       = module.eks_cluster.cluster_id
}

output "cluster_arn" {
  description = "The Amazon Resource Name (ARN) of the cluster"
  value       = module.eks_cluster.cluster_arn
}

output "cluster_endpoint" {
  description = "The endpoint for the Kubernetes API server"
  value       = module.eks_cluster.cluster_endpoint
}

output "cluster_security_group_id" {
  description = "Security group ID attached to the EKS cluster"
  value       = module.eks_cluster.cluster_security_group_id
}

output "cluster_iam_role_name" {
  description = "IAM role name of the EKS cluster"
  value       = module.eks_cluster.cluster_iam_role_name
}

output "cluster_oidc_issuer_url" {
  description = "The URL on the EKS cluster for the OpenID Connect identity provider"
  value       = module.eks_cluster.cluster_oidc_issuer_url
}

output "cluster_certificate_authority_data" {
  description = "Base64 encoded certificate data required to communicate with the cluster"
  value       = module.eks_cluster.cluster_certificate_authority_data
}

output "node_group_id" {
  description = "EKS node group ID"
  value       = module.managed_node_groups.node_group_id
}

output "node_group_arn" {
  description = "Amazon Resource Name (ARN) of the EKS Node Group"
  value       = module.managed_node_groups.node_group_arn
}

output "node_group_status" {
  description = "Status of the EKS Node Group"
  value       = module.managed_node_groups.node_group_status
}

output "vpc_id" {
  description = "The ID of the VPC"
  value       = module.vpc.vpc_id
}

output "private_subnet_ids" {
  description = "List of private subnet IDs"
  value       = module.vpc.private_subnet_ids
}

output "public_subnet_ids" {
  description = "List of public subnet IDs"
  value       = module.vpc.public_subnet_ids
}

output "nat_gateway_public_ips" {
  description = "List of NAT Gateway public IPs"
  value       = module.vpc.nat_gateway_public_ips
}

output "kms_key_arn" {
  description = "The ARN of the KMS key used for EKS secrets encryption"
  value       = module.eks_cluster.kms_key_arn
}

output "addons" {
  description = "Map of EKS add-ons and their details"
  value = {
    vpc_cni = module.eks_addons.vpc_cni_addon
    coredns = module.eks_addons.coredns_addon
    kube_proxy = module.eks_addons.kube_proxy_addon
    ebs_csi_driver = module.eks_addons.ebs_csi_driver_addon
    cloudwatch_observability = module.eks_addons.cloudwatch_observability_addon
    guardduty = module.eks_addons.guardduty_addon
  }
}
