# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

# Crash log files
crash.log
crash.*.log

# Exclude all .tfvars files, which are likely to contain sensitive data
*.tfvars
!terraform.tfvars.example

# Ignore override files as they are usually used for local development
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Ignore CLI configuration files
.terraformrc
terraform.rc

# Ignore lock files
.terraform.lock.hcl

# Ignore Mac/OSX files
.DS_Store

# Ignore Windows files
Thumbs.db
ehthumbs.db
Desktop.ini

# Ignore IDE files
.idea/
.vscode/
*.swp
*.swo

# Ignore log files
*.log

# Ignore any local files
*.local 