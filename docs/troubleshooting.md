# Troubleshooting Guide

This document provides guidance for troubleshooting common issues in the EKS cluster deployed using this Terraform module.

## Cluster Issues

### 1. Cluster Creation Failures

#### Symptoms

- Terraform apply fails
- Cluster status shows "CREATE_FAILED"
- CloudWatch logs show errors

#### Troubleshooting Steps

```bash
# Check CloudWatch logs
aws logs get-log-events --log-group-name /aws/eks/azni-eks-cluster/cluster

# Check IAM roles
aws iam get-role --role-name azni-eks-cluster-role

# Verify VPC configuration
aws ec2 describe-vpcs --vpc-ids <vpc-id>
```

#### Common Solutions

- Verify IAM permissions
- Check VPC configuration
- Ensure sufficient IP addresses
- Verify security group rules

### 2. Node Group Issues

#### Symptoms

- Nodes not joining cluster
- Node group status shows "CREATE_FAILED"
- Pods in pending state

#### Troubleshooting Steps

```bash
# Check node group status
aws eks describe-nodegroup --cluster-name azni-eks-cluster --nodegroup-name <nodegroup-name>

# Check node logs
kubectl logs -n kube-system <node-name>

# Check node conditions
kubectl describe node <node-name>
```

#### Common Solutions

- Verify IAM roles
- Check security group rules
- Ensure sufficient capacity
- Verify subnet configuration

## Networking Issues

### 1. Pod Networking

#### Symptoms

- Pods cannot communicate
- DNS resolution fails
- Network policies not working

#### Troubleshooting Steps

```bash
# Check CNI logs
kubectl logs -n kube-system -l k8s-app=aws-node

# Verify network policies
kubectl get networkpolicy --all-namespaces

# Test pod connectivity
kubectl exec <pod-name> -- ping <target-pod-ip>
```

#### Common Solutions

- Verify CNI configuration
- Check network policies
- Verify security group rules
- Check VPC CNI add-on

### 2. Load Balancer Issues

#### Symptoms

- Services not accessible
- Load balancer creation fails
- Health checks failing

#### Troubleshooting Steps

```bash
# Check load balancer status
aws elbv2 describe-load-balancers

# Check target group health
aws elbv2 describe-target-health --target-group-arn <target-group-arn>

# Verify service configuration
kubectl describe service <service-name>
```

#### Common Solutions

- Verify IAM permissions
- Check security group rules
- Verify subnet configuration
- Check health check settings

## Storage Issues

### 1. Persistent Volume Issues

#### Symptoms

- PVCs in pending state
- Pods cannot mount volumes
- Storage class not found

#### Troubleshooting Steps

```bash
# Check storage class
kubectl get storageclass

# Check PVC status
kubectl get pvc --all-namespaces

# Check EBS CSI driver logs
kubectl logs -n kube-system -l app=ebs-csi-controller
```

#### Common Solutions

- Verify EBS CSI driver
- Check IAM permissions
- Verify storage class
- Check volume limits

### 2. EBS Volume Issues

#### Symptoms

- Volume attachment fails
- IOPS performance issues
- Volume encryption issues

#### Troubleshooting Steps

```bash
# Check volume status
aws ec2 describe-volumes --volume-ids <volume-id>

# Check volume attachments
aws ec2 describe-volume-status --volume-ids <volume-id>

# Verify KMS key
aws kms describe-key --key-id <key-id>
```

#### Common Solutions

- Verify volume type
- Check IOPS configuration
- Verify encryption settings
- Check volume limits

## Security Issues

### 1. IAM Role Issues

#### Symptoms

- Pods cannot access AWS services
- Service account issues
- Permission denied errors

#### Troubleshooting Steps

```bash
# Check IAM role
aws iam get-role --role-name <role-name>

# Check role policy
aws iam get-role-policy --role-name <role-name> --policy-name <policy-name>

# Verify service account
kubectl describe serviceaccount <service-account-name>
```

#### Common Solutions

- Verify IAM policies
- Check trust relationships
- Verify service account
- Check OIDC provider

### 2. Security Group Issues

#### Symptoms

- Network connectivity issues
- Pod-to-pod communication fails
- External access issues

#### Troubleshooting Steps

```bash
# Check security group rules
aws ec2 describe-security-groups --group-ids <security-group-id>

# Verify network policies
kubectl get networkpolicy --all-namespaces

# Test connectivity
kubectl exec <pod-name> -- curl <target-url>
```

#### Common Solutions

- Verify security group rules
- Check network policies
- Verify subnet configuration
- Check VPC endpoints

## Monitoring Issues

### 1. CloudWatch Issues

#### Symptoms

- Metrics not appearing
- Logs not being collected
- Alarms not triggering

#### Troubleshooting Steps

```bash
# Check CloudWatch logs
aws logs describe-log-groups

# Check metrics
aws cloudwatch list-metrics --namespace AWS/EKS

# Verify alarm configuration
aws cloudwatch describe-alarms
```

#### Common Solutions

- Verify IAM permissions
- Check log group configuration
- Verify metric filters
- Check alarm configuration

### 2. Prometheus Issues

#### Symptoms

- Metrics not being collected
- Grafana dashboards not working
- Alertmanager not sending alerts

#### Troubleshooting Steps

```bash
# Check Prometheus status
kubectl get pods -n monitoring -l app=prometheus

# Check Grafana status
kubectl get pods -n monitoring -l app=grafana

# Verify Alertmanager
kubectl get pods -n monitoring -l app=alertmanager
```

#### Common Solutions

- Verify Prometheus configuration
- Check service monitor configuration
- Verify alert rules
- Check storage configuration

## Additional Resources

- [EKS Troubleshooting](https://docs.aws.amazon.com/eks/latest/userguide/troubleshooting.html)
- [Kubernetes Debugging](https://kubernetes.io/docs/tasks/debug-application-cluster/)
- [AWS Support](https://aws.amazon.com/support/)
