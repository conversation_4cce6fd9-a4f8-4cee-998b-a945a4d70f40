# Security Best Practices

This document outlines security best practices for the EKS cluster deployed using this Terraform module.

## Identity and Access Management (IAM)

### 1. IAM Roles and Policies

- Use least privilege principle
- Implement role-based access control (RBAC)
- Regular access reviews
- Rotate credentials regularly

### 2. Service Accounts

```yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: app-service-account
  namespace: default
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::<ACCOUNT_ID>:role/<ROLE_NAME>
```

### 3. OIDC Integration

```hcl
resource "aws_iam_openid_connect_provider" "eks" {
  client_id_list  = ["sts.amazonaws.com"]
  thumbprint_list = ["9e99a48a9960b14926bb7f3b02e22da2b0ab7280"]
  url             = module.eks.cluster_oidc_issuer_url
}
```

## Network Security

### 1. VPC Configuration

- Use private subnets for worker nodes
- Implement security groups
- Configure network ACLs
- Use VPC endpoints

### 2. Network Policies

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny
  namespace: default
spec:
  podSelector: {}
  policyTypes:
    - Ingress
    - Egress
```

### 3. Load Balancer Security

- Use internal load balancers
- Implement SSL/TLS
- Configure security groups
- Use WAF for public-facing ALBs

## Data Security

### 1. Encryption at Rest

```hcl
resource "aws_kms_key" "eks" {
  description             = "KMS key for EKS secrets encryption"
  deletion_window_in_days = 7
  enable_key_rotation     = true
}
```

### 2. Encryption in Transit

- Use TLS 1.2 or higher
- Implement certificate management
- Configure appropriate cipher suites
- Use AWS Certificate Manager

### 3. Secrets Management

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
type: Opaque
data:
  username: <base64-encoded>
  password: <base64-encoded>
```

## Container Security

### 1. Image Security

- Use trusted base images
- Scan images for vulnerabilities
- Implement image signing
- Regular image updates

### 2. Pod Security

```yaml
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: restricted
spec:
  privileged: false
  seLinux:
    rule: RunAsAny
  runAsUser:
    rule: MustRunAsNonRoot
```

### 3. Runtime Security

- Implement security contexts
- Use read-only root filesystems
- Drop unnecessary capabilities
- Implement resource limits

## Monitoring and Logging

### 1. CloudWatch Logs

```hcl
resource "aws_cloudwatch_log_group" "eks" {
  name              = "/aws/eks/${var.cluster_name}/cluster"
  retention_in_days = 30
}
```

### 2. GuardDuty

- Enable GuardDuty
- Configure findings
- Set up alerts
- Regular review of findings

### 3. Security Hub

- Enable Security Hub
- Configure standards
- Review findings
- Implement remediation

## Compliance and Governance

### 1. Tagging Strategy

```hcl
tags = {
  Environment = var.app_environment
  Project     = var.project_name
  Owner       = var.owner
  Compliance  = "HIPAA"
}
```

### 2. Audit Logging

- Enable API server audit logs
- Configure CloudTrail
- Implement log retention
- Regular log review

### 3. Compliance Checks

- Regular security assessments
- Vulnerability scanning
- Penetration testing
- Compliance reporting

## Incident Response

### 1. Detection

- Set up security alerts
- Configure monitoring
- Implement logging
- Regular review

### 2. Response

- Document procedures
- Define roles
- Set up communication
- Regular testing

### 3. Recovery

- Backup procedures
- Recovery testing
- Documentation
- Lessons learned

## Additional Resources

- [AWS Security Best Practices](https://aws.amazon.com/architecture/security-identity-compliance/)
- [Kubernetes Security](https://kubernetes.io/docs/concepts/security/)
- [EKS Security](https://docs.aws.amazon.com/eks/latest/userguide/security.html)
