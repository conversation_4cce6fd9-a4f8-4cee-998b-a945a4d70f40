# Cost Estimation Guide

This document provides an overview of the estimated costs for running an EKS cluster using this Terraform module. Costs are approximate and may vary based on your specific usage patterns and AWS pricing in your region.

## Infrastructure Components and Costs

### EKS Control Plane

- EKS Control Plane: $73.00 per month
- CloudWatch Logs: $0.50 per GB ingested, $0.03 per GB stored
- KMS Key: $1.00 per month

### Node Groups

Costs vary based on instance types and number of nodes:

#### Example Node Group Configurations

1. **Development Environment**

   - Instance Type: t3.medium
   - Nodes: 2
   - Estimated Cost: ~$60.00 per month

2. **Production Environment**
   - Instance Type: m5.large
   - Nodes: 3
   - Estimated Cost: ~$180.00 per month

### Networking

- NAT Gateway: $32.40 per month per AZ
- VPC Endpoints: $0.01 per hour per endpoint
- Data Transfer: $0.09 per GB (outbound)

### Storage

- EBS Volumes: $0.10 per GB-month
- Example: 100GB per node = $10.00 per node per month

### Add-ons

- VPC CNI: No additional cost
- CoreDNS: No additional cost
- kube-proxy: No additional cost
- EBS CSI Driver: No additional cost
- CloudWatch Observability: $0.30 per container per month
- GuardDuty: $0.30 per GB of data processed

## Cost Optimization Recommendations

1. **Right-size Node Groups**

   - Use appropriate instance types for your workload
   - Implement cluster autoscaling
   - Consider spot instances for non-critical workloads

2. **Network Optimization**

   - Use single NAT Gateway for non-production environments
   - Implement VPC endpoints to reduce NAT Gateway costs
   - Optimize data transfer patterns

3. **Storage Optimization**

   - Use appropriate EBS volume types
   - Implement storage class policies
   - Clean up unused volumes

4. **Monitoring and Alerts**
   - Set up CloudWatch alarms for cost monitoring
   - Implement budget alerts
   - Regular cost reviews

## Cost Calculation Example

### Development Environment

```
EKS Control Plane:     $73.00
Node Group (2 nodes):  $60.00
NAT Gateway:          $32.40
Storage (200GB):      $20.00
CloudWatch Logs:      $5.00
--------------------------------
Total:               $190.40/month
```

### Production Environment

```
EKS Control Plane:     $73.00
Node Group (3 nodes):  $180.00
NAT Gateway (2 AZs):  $64.80
Storage (300GB):      $30.00
CloudWatch Logs:      $10.00
GuardDuty:            $15.00
--------------------------------
Total:               $372.80/month
```

## Cost Monitoring

1. **AWS Cost Explorer**

   - Set up cost allocation tags
   - Create cost reports
   - Monitor trends

2. **CloudWatch Alarms**

   - Set up billing alarms
   - Monitor resource usage
   - Alert on cost spikes

3. **Regular Reviews**
   - Weekly cost analysis
   - Monthly optimization review
   - Quarterly architecture review

## Additional Resources

- [AWS Pricing Calculator](https://calculator.aws/)
- [AWS Cost Optimization Best Practices](https://aws.amazon.com/cost-optimization/)
- [EKS Pricing](https://aws.amazon.com/eks/pricing/)
