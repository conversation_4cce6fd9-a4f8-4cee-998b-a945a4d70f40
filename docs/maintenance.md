# Maintenance Guide

This document outlines the maintenance procedures for the EKS cluster deployed using this Terraform module.

## Regular Maintenance Tasks

### 1. Cluster Updates

#### EKS Version Updates

```bash
# Check current version
kubectl version --short

# Update cluster version
aws eks update-cluster-version \
  --cluster-name azni-eks-cluster \
  --kubernetes-version 1.28
```

#### Node Group Updates

```bash
# Update node group
aws eks update-nodegroup-version \
  --cluster-name azni-eks-cluster \
  --nodegroup-name <nodegroup-name> \
  --kubernetes-version 1.28
```

### 2. Add-on Updates

#### VPC CNI

```bash
# Check current version
kubectl describe daemonset aws-node -n kube-system

# Update VPC CNI
kubectl apply -f https://raw.githubusercontent.com/aws/amazon-vpc-cni-k8s/master/config/master/aws-k8s-cni.yaml
```

#### CoreDNS

```bash
# Check current version
kubectl describe deployment coredns -n kube-system

# Update CoreDNS
kubectl apply -f https://raw.githubusercontent.com/coredns/deployment/master/kubernetes/coredns.yaml
```

### 3. Security Updates

#### IAM Role Updates

```bash
# Update IAM role
aws iam update-role \
  --role-name azni-eks-cluster-role \
  --description "Updated EKS cluster role"

# Update role policy
aws iam put-role-policy \
  --role-name azni-eks-cluster-role \
  --policy-name <policy-name> \
  --policy-document file://policy.json
```

#### Security Group Updates

```bash
# Update security group
aws ec2 update-security-group-rule-descriptions-ingress \
  --group-id <security-group-id> \
  --ip-permissions file://ip-permissions.json
```

## Backup and Recovery

### 1. State Backup

```bash
# Backup Terraform state
terraform state pull > terraform.tfstate.backup

# Backup kubeconfig
cp ~/.kube/config ~/.kube/config.backup
```

### 2. Data Backup

```bash
# Backup etcd
kubectl get all --all-namespaces -o yaml > cluster-backup.yaml

# Backup persistent volumes
aws ec2 create-snapshot --volume-id <volume-id> --description "EBS backup"
```

### 3. Configuration Backup

```bash
# Backup add-ons
kubectl get addons --all-namespaces -o yaml > addons-backup.yaml

# Backup network policies
kubectl get networkpolicy --all-namespaces -o yaml > network-policies-backup.yaml
```

## Monitoring and Alerts

### 1. CloudWatch Alarms

```bash
# Create CPU utilization alarm
aws cloudwatch put-metric-alarm \
  --alarm-name eks-cpu-utilization \
  --alarm-description "CPU utilization high" \
  --metric-name CPUUtilization \
  --namespace AWS/EKS \
  --statistic Average \
  --period 300 \
  --threshold 80 \
  --comparison-operator GreaterThanThreshold \
  --evaluation-periods 2 \
  --alarm-actions <sns-topic-arn>
```

### 2. Prometheus Alerts

```yaml
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: eks-alerts
  namespace: monitoring
spec:
  groups:
    - name: eks
      rules:
        - alert: HighCPUUsage
          expr: node_cpu_seconds_total{mode="idle"} < 20
          for: 5m
          labels:
            severity: warning
```

## Health Checks

### 1. Cluster Health

```bash
# Check cluster status
aws eks describe-cluster --cluster-name azni-eks-cluster

# Check node health
kubectl get nodes
kubectl describe nodes
```

### 2. Component Health

```bash
# Check system pods
kubectl get pods -n kube-system

# Check add-ons
kubectl get pods -n kube-system -l k8s-app=aws-node
kubectl get pods -n kube-system -l k8s-app=kube-dns
```

## Resource Management

### 1. Resource Cleanup

```bash
# Clean up unused volumes
kubectl get pvc --all-namespaces
kubectl delete pvc <unused-pvc>

# Clean up unused load balancers
aws elbv2 describe-load-balancers
aws elbv2 delete-load-balancer --load-balancer-arn <unused-lb-arn>
```

### 2. Cost Optimization

```bash
# Check resource usage
kubectl top nodes
kubectl top pods --all-namespaces

# Check CloudWatch metrics
aws cloudwatch get-metric-statistics \
  --namespace AWS/EKS \
  --metric-name CPUUtilization \
  --dimensions Name=ClusterName,Value=azni-eks-cluster \
  --start-time $(date -u +"%Y-%m-%dT%H:%M:%SZ" -d "-1 day") \
  --end-time $(date -u +"%Y-%m-%dT%H:%M:%SZ") \
  --period 3600 \
  --statistics Average
```

## Documentation

### 1. Runbook Updates

- Update procedures
- Document changes
- Review and approve
- Distribute to team

### 2. Configuration Updates

- Update variables
- Document changes
- Test changes
- Apply changes

### 3. Knowledge Base

- Document issues
- Share solutions
- Update FAQs
- Maintain wiki

## Additional Resources

- [EKS Maintenance](https://docs.aws.amazon.com/eks/latest/userguide/maintenance.html)
- [Kubernetes Maintenance](https://kubernetes.io/docs/tasks/administer-cluster/maintenance/)
- [AWS Well-Architected Framework](https://aws.amazon.com/architecture/well-architected/)
