# AWS EKS Cluster Terraform Module

This Terraform module provides a complete, production-ready AWS EKS cluster setup with best practices for security, scalability, and maintainability.

## Features

- **Modular Architecture**: Separate modules for VPC, security groups, IAM roles, EKS cluster, node groups, and add-ons
- **Security Best Practices**:
  - Private subnets for worker nodes
  - Security groups with least privilege
  - IAM roles with minimal permissions
  - KMS encryption for secrets
  - GuardDuty integration
- **High Availability**:
  - Multi-AZ deployment
  - Managed node groups with auto-scaling
  - NAT gateways for private subnet internet access
- **Observability**:
  - CloudWatch logging
  - CloudWatch observability add-on
  - VPC flow logs
- **Add-ons**:
  - VPC CNI
  - CoreDNS
  - kube-proxy
  - EBS CSI driver
  - AWS Load Balancer Controller
  - GuardDuty agent

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                         VPC Module                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ Public      │  │ Private     │  │ NAT Gateways        │  │
│  │ Subnets     │  │ Subnets     │  │                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                    Security Groups Module                    │
│  ┌─────────────────────┐  ┌─────────────────────────────┐   │
│  │ Cluster Security    │  │ Node Security Groups        │   │
│  │ Groups              │  │                             │   │
│  └─────────────────────┘  └─────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                    IAM Roles Module                         │
│  ┌─────────────────────┐  ┌─────────────────────────────┐   │
│  │ Cluster Role        │  │ Node Role                   │   │
│  │                     │  │                             │   │
│  └─────────────────────┘  └─────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                    EKS Cluster Module                       │
│  ┌─────────────────────┐  ┌─────────────────────────────┐   │
│  │ Control Plane       │  │ OIDC Provider              │   │
│  │                     │  │                             │   │
│  └─────────────────────┘  └─────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                Managed Node Groups Module                    │
│  ┌─────────────────────┐  ┌─────────────────────────────┐   │
│  │ Auto Scaling        │  │ Node Configuration          │   │
│  │ Groups              │  │                             │   │
│  └─────────────────────┘  └─────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                    EKS Add-ons Module                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ VPC CNI     │  │ CoreDNS     │  │ kube-proxy         │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ EBS CSI     │  │ CloudWatch  │  │ GuardDuty          │  │
│  │ Driver      │  │ Observability│  │ Agent             │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## Prerequisites

- Terraform >= 1.0.0
- AWS CLI configured with appropriate credentials
- kubectl installed
- aws-iam-authenticator installed

## Usage

1. Clone the repository:

```bash
git clone <repository-url>
cd <repository-directory>
```

2. Create a `terraform.tfvars` file with your configuration:

```hcl
aws_region = "us-east-1"
cluster_name = "my-eks-cluster"
vpc_cidr = "10.0.0.0/16"
app_environment = "production"
project_name = "my-project"
owner = "DevOps"

# Node group configuration
mng_instance_types = ["m5.large", "m5.xlarge"]
mng_disk_size_gb = 50
mng_min_nodes = 2
mng_max_nodes = 5
mng_desired_nodes = 3

# Security configuration
enable_secrets_encryption = true
enable_cloudwatch_logging = true
enable_guardduty = true
```

3. Initialize Terraform:

```bash
terraform init
```

4. Plan the deployment:

```bash
terraform plan
```

5. Apply the configuration:

```bash
terraform apply
```

6. Configure kubectl:

```bash
aws eks update-kubeconfig --name my-eks-cluster --region us-east-1
```

## Module Structure

```
.
├── main.tf                 # Root module configuration
├── variables.tf            # Root module variables
├── outputs.tf             # Root module outputs
├── versions.tf            # Provider and version constraints
├── modules/
│   ├── vpc/               # VPC and networking
│   ├── security_groups/   # Security group configurations
│   ├── iam_roles_policies/# IAM roles and policies
│   ├── eks_cluster/       # EKS cluster configuration
│   ├── managed_node_groups/# Node group configuration
│   └── eks_addons/        # EKS add-ons configuration
└── README.md
```

## Security Considerations

1. **Network Security**:

   - Private subnets for worker nodes
   - Security groups with least privilege
   - VPC flow logs enabled
   - NAT gateways for private subnet internet access

2. **IAM Security**:

   - IAM roles with minimal permissions
   - IRSA (IAM Roles for Service Accounts) enabled
   - Separate roles for cluster and nodes

3. **Data Security**:
   - KMS encryption for secrets
   - EBS encryption enabled
   - GuardDuty integration for threat detection

## Best Practices

1. **Resource Naming**:

   - Use consistent naming conventions
   - Include environment and project tags
   - Use descriptive names for resources

2. **State Management**:

   - Use remote state storage
   - Enable state locking
   - Use workspaces for different environments

3. **Cost Optimization**:

   - Use spot instances where appropriate
   - Implement auto-scaling
   - Monitor resource usage

4. **Maintenance**:
   - Regular updates of EKS version
   - Monitor add-on versions
   - Regular security group reviews

## Troubleshooting

Common issues and solutions:

1. **Node Group Scaling Issues**:

   - Check IAM permissions
   - Verify subnet capacity
   - Review auto-scaling group metrics

2. **Add-on Installation Failures**:

   - Verify IAM roles
   - Check cluster version compatibility
   - Review CloudWatch logs

3. **Network Connectivity Issues**:
   - Verify security group rules
   - Check NAT gateway status
   - Review VPC flow logs

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
