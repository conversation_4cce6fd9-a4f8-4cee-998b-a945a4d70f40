# VPC Module
module "vpc" {
  source = "./modules/vpc"

  vpc_cidr             = var.vpc_cidr
  cluster_name         = var.cluster_name
  app_environment      = var.app_environment
  project_name         = var.project_name
  owner                = var.owner
  azs                  = var.availability_zones
  private_subnet_cidrs = var.private_subnet_cidrs
  public_subnet_cidrs  = var.public_subnet_cidrs
}

# Security Groups Module
module "security_groups" {
  source = "./modules/security_groups"

  vpc_id          = module.vpc.vpc_id
  cluster_name    = var.cluster_name
  app_environment = var.app_environment
  project_name    = var.project_name
  owner           = var.owner
  vpc_cidr        = var.vpc_cidr
  additional_node_security_group_rules = var.additional_node_security_group_rules
}

# IAM Roles and Policies Module
module "iam_roles_policies" {
  source = "./modules/iam_roles_policies"

  cluster_name                   = var.cluster_name
  app_environment                = var.app_environment
  project_name                   = var.project_name
  owner                          = var.owner
  enable_irsa                    = var.enable_irsa
  oidc_provider_arn              = module.eks_cluster.cluster_oidc_provider_arn
  enable_ebs_csi_driver          = var.enable_ebs_csi_driver
  enable_load_balancer_controller = var.enable_aws_load_balancer_controller
}

# EKS Cluster Module
module "eks_cluster" {
  source = "./modules/eks_cluster"

  cluster_name           = var.cluster_name
  app_environment        = var.app_environment
  project_name           = var.project_name
  owner                  = var.owner
  cluster_role_arn       = module.iam_roles_policies.cluster_role_arn
  subnet_ids             = module.vpc.private_subnet_ids
  security_group_ids     = [module.security_groups.cluster_security_group_id]
  enable_irsa            = var.enable_irsa
  enable_secrets_encryption = var.enable_secrets_encryption
  enable_cloudwatch_logging  = var.enable_cloudwatch_logging
  trusted_ips_for_api    = var.trusted_ips_for_api
  cluster_version        = var.cluster_version
}

# Managed Node Groups Module
module "managed_node_groups" {
  source = "./modules/managed_node_groups"

  cluster_name   = var.cluster_name
  app_environment = var.app_environment
  project_name    = var.project_name
  owner           = var.owner
  node_role_arn   = module.iam_roles_policies.node_role_arn
  subnet_ids      = module.vpc.private_subnet_ids
  instance_types  = var.mng_instance_types
  disk_size       = var.mng_disk_size_gb
  min_size        = var.mng_min_nodes
  max_size        = var.mng_max_nodes
  desired_size    = var.mng_desired_nodes
  labels          = var.mng_labels
  taints          = var.mng_taints
}

# EKS Add-ons Module
module "eks_addons" {
  source = "./modules/eks_addons"

  cluster_name      = var.cluster_name
  app_environment   = var.app_environment
  project_name      = var.project_name
  owner             = var.owner
  enable_vpc_cni    = true
  enable_coredns    = true
  enable_kube_proxy = true
  enable_ebs_csi_driver = var.enable_ebs_csi_driver
  enable_cloudwatch_observability = var.enable_cloudwatch_logging
  enable_guardduty  = var.enable_guardduty
  ebs_csi_driver_role_arn = module.iam_roles_policies.ebs_csi_driver_role_arn
  vpc_cni_config = {
    enable_prefix_delegation = true
    warm_eni_target         = 1
  }
}
